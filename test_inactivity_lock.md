# Test Plan for Inactivity Lock System

## Functionality Implemented

### 1. Inactivity Detection
- **Timeout**: 3 seconds (configurable via `INACTIVITY_TIMEOUT_SECONDS` constant)
- **Activity Detection**: Mouse movement, mouse clicks, key presses, and key typing
- **Timer Management**: Automatic reset on activity, stops when screen is locked

### 2. Screen Lock Overlay
- **UI**: Semi-transparent black overlay with white text
- **Components**: 
  - "Sistema Bloqueado" label
  - PasswordField with prompt text "Ingrese RFID"
- **Behavior**: Covers entire interface, only RFID field is interactive

### 3. RFID Authentication
- **Same User**: Unlocks screen and resumes normal operation
- **Different User**: Performs user switch without navigation, stays in UniversalSaleGuiController
- **Error Handling**: Red border on field for 2 seconds on authentication failure

### 4. User Switching Logic
- Cleans up current subscriptions
- Updates security context with new user
- Reinitializes subscriptions for new user
- Reloads user menu
- Updates user info label

## Manual Testing Steps

1. **Start Application**: Navigate to UniversalSaleGuiController
2. **Test Inactivity**: Wait 3 seconds without moving mouse or pressing keys
3. **Verify Lock**: Screen should show black overlay with RFID field
4. **Test Same User**: Enter current user's RFID and press Enter
5. **Verify Unlock**: Screen should unlock and resume normal operation
6. **Test Different User**: Lock screen again, enter different user's RFID
7. **Verify User Switch**: Should switch user without leaving UniversalSaleGuiController
8. **Test Error Handling**: Enter invalid RFID, verify red border appears

## Edge Cases Handled

- Screen lock state management (prevents multiple locks)
- Timer cleanup on controller close
- Proper focus management on lock/unlock
- Error visual feedback with automatic reset
- Subscription management during user switching

## Configuration

To change the inactivity timeout, modify the constant:
```java
private static final double INACTIVITY_TIMEOUT_SECONDS = 3.0; // Change this value
```
<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<StackPane fx:id="mainStackPane" prefHeight="800.0" prefWidth="1600.0" stylesheets="@../../css/styles.css" xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.caja.controller.CajaPrincipalController">
   <children>
      <!-- Loading indicator -->
      <ProgressIndicator fx:id="loadingIndicator" visible="false" />

      <!-- Main content container -->
      <VBox fx:id="mainContent" spacing="20.0" visible="false">
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>

         <!-- Header section -->
         <HBox alignment="CENTER_LEFT" spacing="15.0">
            <children>
               <Label fx:id="cajaNameLabel" text="Caja: [No seleccionada]">
                  <font>
                     <Font name="System Bold" size="16.0" />
                  </font>
               </Label>
               <Region HBox.hgrow="ALWAYS" />
               <MenuButton fx:id="cajaMenuButton" styleClass="nav-button" text="Gestión de Caja">
                  <font>
                     <Font name="System Bold" size="12.0" />
                  </font>
                  <items>
                     <MenuItem fx:id="menuChangeCaja" onAction="#onChangeCaja" text="Cambiar Caja" />
                     <SeparatorMenuItem />
                     <MenuItem fx:id="menuInitEfectivo" onAction="#onInitializeCajaEfectivo" text="Iniciar Caja Efectivo" />
                     <MenuItem fx:id="menuCloseEfectivo" onAction="#onCloseCajaEfectivo" text="Cerrar Caja Efectivo" />
                     <SeparatorMenuItem />
                     <MenuItem fx:id="menuInitDigital" onAction="#onInitializeCajaDigital" text="Iniciar Caja Digital" />
                     <MenuItem fx:id="menuCloseDigital" onAction="#onCloseCajaDigital" text="Cerrar Caja Digital" />
                  </items>
               </MenuButton>
               <CheckBox fx:id="leerVentaCheckBox" selected="true" text="Leer Venta">
                  <font>
                     <Font name="System Bold" size="12.0" />
                  </font>
               </CheckBox>
               <Button fx:id="refreshButton" onAction="#onRefresh" text="Actualizar">
                  <font>
                     <Font name="System Bold" size="12.0" />
                  </font>
               </Button>
            </children>
         </HBox>

         <!-- Split pane with detail view on left and tables on right -->
         <SplitPane fx:id="splitPaneMain" dividerPositions="0.3" VBox.vgrow="ALWAYS">
            <items>
               <!-- Left side: Cobro detail view -->
               <AnchorPane fx:id="anchorCobroDetail" maxWidth="630.0" minHeight="0.0" minWidth="300.0">
                  <!-- This will be populated with the cobro detail view -->
               </AnchorPane>

               <!-- Right side: Tables with collections -->
               <VBox spacing="10.0">
                  <children>
                     <!-- Tabs for different collection types -->
                     <TabPane fx:id="collectionsTabPane" tabClosingPolicy="UNAVAILABLE" VBox.vgrow="ALWAYS">
                        <tabs>
                           <Tab text="Contado">
                              <content>
                                 <VBox spacing="10.0">
                                    <children>
                                       <Label text="Cobros Pendientes - Contado">
                                          <font>
                                             <Font name="System Bold" size="14.0" />
                                          </font>
                                       </Label>
                                       <TableView fx:id="contadoTableView" VBox.vgrow="ALWAYS">
                                          <columns>
                                             <TableColumn fx:id="contadoIdColumn" prefWidth="120.0" text="Iniciado Por" />
                                             <TableColumn fx:id="contadoMontoColumn" prefWidth="120.0" text="Monto a Cobrar" />
                                             <TableColumn fx:id="contadoRestanteColumn" prefWidth="120.0" text="Monto Restante" />
                                             <TableColumn fx:id="contadoCreadoElColumn" prefWidth="150.0" text="Creado El" />
                                             <TableColumn fx:id="contadoFechaColumn" prefWidth="150.0" text="Fecha Límite" />
                                          </columns>
                                       </TableView>
                                    </children>
                                 </VBox>
                              </content>
                           </Tab>
                           <Tab text="Crédito">
                              <content>
                                 <VBox spacing="10.0">
                                    <children>
                                       <Label text="Cobros Pendientes - Crédito">
                                          <font>
                                             <Font name="System Bold" size="14.0" />
                                          </font>
                                       </Label>
                                       <TableView fx:id="creditoTableView" VBox.vgrow="ALWAYS">
                                          <columns>
                                             <TableColumn fx:id="creditoIdColumn" prefWidth="120.0" text="Iniciado Por" />
                                             <TableColumn fx:id="creditoMontoColumn" prefWidth="120.0" text="Monto a Cobrar" />
                                             <TableColumn fx:id="creditoRestanteColumn" prefWidth="120.0" text="Monto Restante" />
                                             <TableColumn fx:id="creditoCreadoElColumn" prefWidth="150.0" text="Creado El" />
                                             <TableColumn fx:id="creditoFechaColumn" prefWidth="150.0" text="Fecha Límite" />
                                          </columns>
                                       </TableView>
                                    </children>
                                 </VBox>
                              </content>
                           </Tab>
                           <Tab text="Pedido">
                              <content>
                                 <VBox spacing="10.0">
                                    <children>
                                       <Label text="Cobros Pendientes - Pedido">
                                          <font>
                                             <Font name="System Bold" size="14.0" />
                                          </font>
                                       </Label>
                                       <TableView fx:id="pedidoTableView" VBox.vgrow="ALWAYS">
                                          <columns>
                                             <TableColumn fx:id="pedidoIdColumn" prefWidth="120.0" text="Iniciado Por" />
                                             <TableColumn fx:id="pedidoMontoColumn" prefWidth="120.0" text="Monto a Cobrar" />
                                             <TableColumn fx:id="pedidoRestanteColumn" prefWidth="120.0" text="Monto Restante" />
                                             <TableColumn fx:id="pedidoCreadoElColumn" prefWidth="150.0" text="Creado El" />
                                             <TableColumn fx:id="pedidoFechaColumn" prefWidth="150.0" text="Fecha Límite" />
                                          </columns>
                                       </TableView>
                                    </children>
                                 </VBox>
                              </content>
                           </Tab>
                        </tabs>
                     </TabPane>
                  </children>
               </VBox>
            </items>
         </SplitPane>
      </VBox>
   </children>
</StackPane>

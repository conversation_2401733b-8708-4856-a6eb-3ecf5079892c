<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ProgressIndicator?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<StackPane fx:id="stackPaneCobroDetail" stylesheets="@../../css/styles.css, @../../css/sale.css" xmlns="http://javafx.com/javafx/21.0.4" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.caja.controller.CobroDetailController">
   <children>
      <AnchorPane fx:id="anchorCobroDetail" maxWidth="630.0" minHeight="0.0" minWidth="300.0">
         <children>
            <!-- Customer/Cobro info section (similar to sale.fxml top section) -->
            <HBox spacing="5.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
               <children>
                  <Label fx:id="lblClienteInfo" text="Cliente: Cargando..." HBox.hgrow="ALWAYS">
                     <font>
                        <Font name="System Bold" size="15.0" />
                     </font>
                  </Label>
               </children>
            </HBox>

            <!-- Cobro totals section (similar to sale.fxml totals) -->
            <VBox fx:id="vbTotalCobro" alignment="CENTER" layoutX="4.0" layoutY="33.0" prefHeight="77.0" prefWidth="173.0" AnchorPane.leftAnchor="0.0" AnchorPane.topAnchor="36.0">
               <children>
                  <AnchorPane>
                     <children>
                        <Label layoutX="92.0" text="A COBRAR:" AnchorPane.rightAnchor="72.0">
                           <font>
                              <Font name="System Bold" size="15.0" />
                           </font>
                        </Label>
                        <Label fx:id="lblMontoACobrar" layoutX="64.0" text="0.00" textAlignment="RIGHT" AnchorPane.rightAnchor="3.0">
                           <font>
                              <Font name="System Bold" size="15.0" />
                           </font>
                        </Label>
                     </children>
                  </AnchorPane>
                  <AnchorPane>
                     <children>
                        <Label text="RESTANTE:" AnchorPane.rightAnchor="72.0">
                           <font>
                              <Font name="System Bold" size="15.0" />
                           </font>
                        </Label>
                        <Label fx:id="lblMontoRestante" layoutX="107.0" text="0.00" textAlignment="RIGHT" AnchorPane.rightAnchor="3.0">
                           <font>
                              <Font name="System Bold" size="15.0" />
                           </font>
                        </Label>
                     </children>
                  </AnchorPane>
                  <AnchorPane>
                     <children>
                        <Label fx:id="lblIniciadoPor" layoutX="9.0" text="Iniciado por: -" textAlignment="RIGHT" AnchorPane.rightAnchor="3.0">
                           <font>
                              <Font name="System Bold" size="12.0" />
                           </font>
                        </Label>
                     </children>
                  </AnchorPane>
               </children>
            </VBox>

            <!-- Fecha limite info (similar to sale.fxml code field) -->
            <Label fx:id="lblFechaLimite" alignment="CENTER" layoutX="182.0" layoutY="39.0" maxHeight="63.0" maxWidth="221.0" minHeight="63.0" minWidth="180.0" prefHeight="63.0" prefWidth="213.0" text="Fecha límite: -" AnchorPane.leftAnchor="182.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="42.0">
               <font>
                  <Font name="System Bold" size="12.0" />
               </font>
            </Label>

            <!-- Sale info display area (similar to sale.fxml ListView) -->
            <ScrollPane fx:id="scrollPaneSaleInfo" layoutX="-5.0" layoutY="106.0" AnchorPane.bottomAnchor="45.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="111.0">
               <content>
                  <VBox fx:id="vbSaleInfo" spacing="8.0">
                     <padding>
                        <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                     </padding>
                     <!-- Sale information will be populated here -->
                  </VBox>
               </content>
            </ScrollPane>

            <!-- Action buttons section (similar to sale.fxml buttons) -->
            <AnchorPane fx:id="anchorBotones" prefHeight="45.0" prefWidth="450.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0">
               <children>
                  <HBox layoutX="22.0" layoutY="10.0" spacing="3.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                     <padding>
                        <Insets bottom="3.0" left="3.0" right="3.0" top="3.0" />
                     </padding>
                     <children>
                        <Button fx:id="btnRefresh" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" mnemonicParsing="false" onAction="#handleBtnRefresh" text="Actualizar" HBox.hgrow="ALWAYS">
                           <font>
                              <Font name="System Bold" size="12.0" />
                           </font>
                        </Button>
                        <Button fx:id="btnVerVenta" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" mnemonicParsing="false" onAction="#handleBtnVerVenta" text="Escanear Venta" HBox.hgrow="ALWAYS">
                           <font>
                              <Font name="System Bold" size="12.0" />
                           </font>
                        </Button>
                        <Button fx:id="btnCobrar" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" mnemonicParsing="false" onAction="#handleBtnCobrar" text="Cobrar" HBox.hgrow="ALWAYS">
                           <font>
                              <Font name="System Bold" size="12.0" />
                           </font>
                        </Button>
                     </children>
                  </HBox>
               </children>
            </AnchorPane>
         </children>
      </AnchorPane>
      <ProgressIndicator fx:id="loadingIndicator" maxHeight="100" maxWidth="100" visible="false" />
   </children>
</StackPane>

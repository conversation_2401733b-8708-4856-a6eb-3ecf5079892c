package corp.jamaro.jamaroescritoriofx.appfx.model;

import lombok.Getter;

/**
 * Enumeración de las vistas FXML disponibles en la aplicación.
 * Cada valor enum contiene la ruta al archivo FXML correspondiente.
 */
@Getter
public enum FXMLEnum {

    LOGIN_RFID("/fxml/loginRfidView.fxml"),
    VENTAS_PRINCIPAL("/fxml/venta/ventasPrincipalView.fxml"),
    VENTA("/fxml/venta/ventaView.fxml"),
    SEARCH_PRODUCT_CATEGORY("/fxml/venta/busquedaproducto/busquedaProductoFiltros.fxml"),

    CREAR_VEHICULO("fxml/vehiculo/vehiculoMantenimiento.fxml"),

    UNIVERSAL_SALE("/fxml/sale/universalSale.fxml"),
    MAIN_SALE("/fxml/sale/mainSale.fxml"),
    SALE_GUI("/fxml/sale/saleGui.fxml"),
    SALE("/fxml/sale/sale.fxml"),
    SEARCH_PRODUCT("/fxml/sale/searchproduct/searchProductGui.fxml"),

    CLIENTE_CREATION("/fxml/cliente/clienteCreation.fxml"),
    MENU_SELECTION("/fxml/menuSelection.fxml"),

    CAJA_PRINCIPAL("/fxml/caja/cajaPrincipal.fxml"),
    COBRO_DETAIL("/fxml/caja/cobroDetail.fxml"),

    MANTENIMIENTO_PRINCIPAL("/fxml/mantenimientoPrincipal.fxml"),
    PRODUCTO_MANTENIMIENTO("/fxml/producto/productoMantenimiento.fxml"),
    ITEM_MANTENIMIENTO("/fxml/producto/itemMantenimiento.fxml"),
    GRUPO_MANTENIMIENTO("/fxml/producto/grupoCategoria.fxml"),
    ;
    private final String path;

    FXMLEnum(String path) {
        this.path = path;
    }

}
//    VENTAS_PRINCIPAL("/fxml/venta/ventasPrincipalView.fxml"),
//    VENTA("/fxml/venta/ventaView.fxml"),
//    SEARCH_PRODUCT_CATEGORY("/fxml/venta/busquedaproducto/busquedaProductoFiltros.fxml"),
////    SEARCH_PRODUCT("/fxml/venta/busquedaproducto/busquedaProductoView.fxml"),

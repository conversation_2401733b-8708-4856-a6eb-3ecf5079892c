package corp.jamaro.jamaroescritoriofx.appfx.ventas.util;

import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Item;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import corp.jamaro.jamaroescritoriofx.appfx.util.EncryptionUtil;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.Sale;
import javafx.application.Platform;
import javafx.beans.property.SimpleStringProperty;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.input.KeyCode;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.VBox;
import javafx.stage.StageStyle;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;

/**
 * Utilidad para crear y mostrar diálogos específicos relacionados con ventas.
 * Centraliza la lógica de diálogos complejos para mantener los controladores más limpios.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SaleDialogUtil {

    /**
     * Clase para encapsular el resultado del parsing de cantidad y precio inicial.
     * Utilizada para manejar el formato mejorado de entrada: "cantidad espacios precio"
     * 
     * @param cantidad La cantidad numérica extraída del input
     * @param precioInicial El precio inicial extraído del input (puede ser null si no se especifica)
     */
    public record CantidadPrecioResult(Double cantidad, Double precioInicial) {
        /**
         * Constructor de conveniencia para casos donde solo se especifica cantidad
         */
        public CantidadPrecioResult(Double cantidad) {
            this(cantidad, null);
        }
    }

    /**
     * Parsea el input del usuario para extraer cantidad y precio inicial usando el formato mejorado.
     * 
     * Formato soportado:
     * - Solo cantidad: "12", "50.5", "40"
     * - Cantidad + precio: "12 EU.L", "50.5   305.5", "40  co"
     * 
     * La lógica es:
     * 1. Todo lo que está antes de los espacios en blanco = cantidad (parte numérica)
     * 2. Todo lo que está después de los espacios en blanco = precio inicial (números o encriptado)
     * 3. Si no hay espacios, mantiene el comportamiento por defecto (solo cantidad)
     * 
     * @param input El texto ingresado por el usuario
     * @return CantidadPrecioResult con la cantidad y precio inicial parseados
     * @throws NumberFormatException si la cantidad no es un número válido
     */
    public CantidadPrecioResult parseEnhancedInput(String input) {
        if (input == null || input.trim().isEmpty()) {
            throw new NumberFormatException("El input no puede estar vacío");
        }

        String trimmedInput = input.trim();
        
        // Buscar el primer grupo de espacios en blanco consecutivos
        int firstSpaceIndex = -1;
        boolean foundNonSpace = false;
        
        for (int i = 0; i < trimmedInput.length(); i++) {
            char c = trimmedInput.charAt(i);
            if (Character.isWhitespace(c)) {
                if (foundNonSpace && firstSpaceIndex == -1) {
                    firstSpaceIndex = i;
                    break;
                }
            } else {
                foundNonSpace = true;
            }
        }
        
        // Si no hay espacios, comportamiento por defecto (solo cantidad)
        if (firstSpaceIndex == -1) {
            log.debug("Parsing input sin espacios: '{}'", trimmedInput);
            double cantidad = Double.parseDouble(trimmedInput);
            return new CantidadPrecioResult(cantidad);
        }
        
        // Extraer cantidad (parte antes de los espacios)
        String cantidadStr = trimmedInput.substring(0, firstSpaceIndex).trim();
        if (cantidadStr.isEmpty()) {
            throw new NumberFormatException("La cantidad no puede estar vacía");
        }
        
        // Extraer precio inicial (parte después de los espacios)
        String precioStr = trimmedInput.substring(firstSpaceIndex).trim();
        
        log.debug("Parsing input con formato mejorado - Cantidad: '{}', Precio: '{}'", cantidadStr, precioStr);
        
        // Parsear cantidad
        double cantidad = Double.parseDouble(cantidadStr);
        
        // Parsear precio inicial
        Double precioInicial = null;
        if (!precioStr.isEmpty()) {
            try {
                // Intentar parsear como número directo
                precioInicial = Double.parseDouble(precioStr);
                log.debug("Precio parseado como número directo: {}", precioInicial);
            } catch (NumberFormatException e) {
                // Si falla, intentar desencriptar usando EncryptionUtil
                try {
                    precioInicial = EncryptionUtil.decryptToDouble(precioStr);
                    if (precioInicial != null) {
                        log.debug("Precio parseado como encriptado: {} -> {}", precioStr, precioInicial);
                    } else {
                        log.warn("No se pudo desencriptar el precio: '{}'", precioStr);
                        throw new NumberFormatException("El precio '" + precioStr + "' no es válido ni como número ni como código encriptado");
                    }
                } catch (Exception encryptionError) {
                    log.warn("Error al desencriptar precio '{}': {}", precioStr, encryptionError.getMessage());
                    throw new NumberFormatException("El precio '" + precioStr + "' no es válido ni como número ni como código encriptado");
                }
            }
        }
        
        return new CantidadPrecioResult(cantidad, precioInicial);
    }

    private static final String STYLESHEET_PATH = "/css/styles.css";
    private static final String SEARCH_PRODUCT_STYLESHEET_PATH = "/css/searchProduct.css";

    private final AlertUtil alertUtil;
    private NumberFormat currencyFormat = NumberFormat.getCurrencyInstance();
    
    // Static list to track active dialogs for screen lock management
    private static final List<Dialog<?>> activeDialogs = new ArrayList<>();

    /**
     * Muestra un mensaje de éxito específico según el tipo de venta procesada.
     */
    public void showVentaProcesamientoExitoso(Sale.TipoVenta tipoVenta, Double totalMontoAcordado) {
        String tipoVentaText = getTipoVentaDisplayText(tipoVenta);
        String montoText = totalMontoAcordado != null ? currencyFormat.format(totalMontoAcordado) : "N/A";

        String title = "Venta Procesada";
        String message = String.format(
                """
                        ✅ %s procesada exitosamente

                        💰 Monto total: %s
                        📋 Estado: %s""",
            tipoVentaText,
            montoText,
            getEstadoVentaText(tipoVenta)
        );

        alertUtil.showInfo(title, message);
        log.info("Venta procesada exitosamente - Tipo: {}, Monto: {}", tipoVenta, montoText);
    }

    /**
     * Muestra un diálogo para seleccionar el tipo de venta.
     */
    public Optional<Sale.TipoVenta> showTipoVentaSelectionDialog(Sale currentSale) {
        Dialog<Sale.TipoVenta> dialog = new Dialog<>();
        dialog.setTitle("Tipo de Venta");

        // Crear botones
        ButtonType aceptarButtonType = new ButtonType("Aceptar", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelarButtonType = new ButtonType("Cancelar", ButtonBar.ButtonData.CANCEL_CLOSE);
        dialog.getDialogPane().getButtonTypes().addAll(aceptarButtonType, cancelarButtonType);

        // Crear contenido organizado con VBox
        VBox contentBox = new VBox(12);
        contentBox.setPadding(new Insets(15));

        // Título
        Label titleLabel = new Label("Seleccione el tipo de venta:");
        titleLabel.getStyleClass().add("dialog-title");

        // ComboBox con opciones
        ComboBox<Sale.TipoVenta> comboBox = new ComboBox<>();

        // Filtrar opciones: agregar solo PROFORMA, CREDITO y PEDIDO (excluir CONTADO)
        List<Sale.TipoVenta> allowedTypes = Arrays.asList(
                Sale.TipoVenta.PROFORMA,
                Sale.TipoVenta.CREDITO,
                Sale.TipoVenta.PEDIDO
        );
        comboBox.getItems().addAll(allowedTypes);

        // Seleccionar el tipo actual si está en la lista permitida
        if (currentSale != null && currentSale.getTipoVenta() != null &&
            allowedTypes.contains(currentSale.getTipoVenta())) {
            comboBox.setValue(currentSale.getTipoVenta());
        } else {
            comboBox.setValue(Sale.TipoVenta.PROFORMA);
        }

        // Configurar ComboBox para mejor visibilidad
        comboBox.setPrefWidth(200);
        comboBox.setMaxWidth(Double.MAX_VALUE);

        contentBox.getChildren().addAll(titleLabel, comboBox);
        dialog.getDialogPane().setContent(contentBox);

        // Aplicar estilos modernos
        applyDialogStyles(dialog);
        setDialogIcon(dialog, "fas-file-invoice");

        // Configurar el resultado del diálogo
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == aceptarButtonType) {
                return comboBox.getValue();
            }
            return null;
        });

        return dialog.showAndWait();
    }

    /**
     * Muestra un diálogo para seleccionar el tipo de venta al momento de procesar la venta.
     * Este diálogo es específico para el proceso de venta y no requiere un Sale existente.
     */
    public Optional<Sale.TipoVenta> showTipoVentaSelectionDialogForSale() {
        Dialog<Sale.TipoVenta> dialog = new Dialog<>();
        dialog.setTitle("Procesar Venta");

        // Crear botones
        ButtonType procesarButtonType = new ButtonType("Procesar Venta", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelarButtonType = new ButtonType("Cancelar", ButtonBar.ButtonData.CANCEL_CLOSE);
        dialog.getDialogPane().getButtonTypes().addAll(procesarButtonType, cancelarButtonType);

        // Crear contenido organizado con VBox
        VBox contentBox = new VBox(15);
        contentBox.setPadding(new Insets(20));
        contentBox.setAlignment(Pos.CENTER);

        // Título
        Label titleLabel = new Label("Seleccione el tipo de venta:");
        titleLabel.getStyleClass().add("dialog-title-large");

        // ComboBox con opciones
        ComboBox<Sale.TipoVenta> comboBox = new ComboBox<>();

        // Agregar todas las opciones disponibles para procesar venta
        List<Sale.TipoVenta> availableTypes = Arrays.asList(
                Sale.TipoVenta.PROFORMA,  // Para venta al contado
                Sale.TipoVenta.CREDITO,
                Sale.TipoVenta.PEDIDO
        );

        comboBox.getItems().addAll(availableTypes);
        comboBox.setValue(Sale.TipoVenta.PROFORMA); // Valor por defecto

        // Configurar el cell factory para mostrar texto personalizado
        comboBox.setCellFactory(listView -> new ListCell<>() {
            @Override
            protected void updateItem(Sale.TipoVenta item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(getTipoVentaDisplayText(item));
                }
            }
        });

        // Configurar el button cell para mostrar el texto seleccionado
        comboBox.setButtonCell(new ListCell<>() {
            @Override
            protected void updateItem(Sale.TipoVenta item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(getTipoVentaDisplayText(item));
                }
            }
        });

        // Configurar ComboBox para mejor visibilidad
        comboBox.setPrefWidth(250);
        comboBox.setMaxWidth(Double.MAX_VALUE);

        // Descripción del tipo seleccionado
        Label descriptionLabel = new Label();
        descriptionLabel.getStyleClass().add("dialog-description");
        descriptionLabel.setWrapText(true);
        descriptionLabel.setMaxWidth(250);

        // Actualizar descripción cuando cambie la selección
        comboBox.valueProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal != null) {
                descriptionLabel.setText(getEstadoVentaText(newVal));
            }
        });

        // Establecer descripción inicial
        descriptionLabel.setText(getEstadoVentaText(Sale.TipoVenta.PROFORMA));

        contentBox.getChildren().addAll(titleLabel, comboBox, descriptionLabel);
        dialog.getDialogPane().setContent(contentBox);

        // Aplicar estilos modernos
        applyDialogStyles(dialog);
        setDialogIcon(dialog, "fas-cash-register");

        // Configurar el resultado del diálogo
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == procesarButtonType) {
                return comboBox.getValue();
            }
            return null;
        });

        // Registrar el diálogo para manejo de bloqueo de pantalla
        activeDialogs.add(dialog);
        
        // Configurar focus en el ComboBox
        Platform.runLater(comboBox::requestFocus);

        // Mostrar el diálogo y limpiar registro al cerrar
        Optional<Sale.TipoVenta> result = dialog.showAndWait();
        activeDialogs.remove(dialog);
        
        return result;
    }

    /**
     * Muestra un diálogo para ingresar la cantidad de un item.
     */
    public Optional<String> showCantidadDialog(String itemCode, String itemDescription) {
        Dialog<String> quantityDialog = new Dialog<>();
        quantityDialog.setTitle("Agregar Item");
        quantityDialog.setHeaderText(null);

        // Configurar botones
        ButtonType aceptarButtonType = new ButtonType("Aceptar", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelarButtonType = new ButtonType("Cancelar", ButtonBar.ButtonData.CANCEL_CLOSE);
        quantityDialog.getDialogPane().getButtonTypes().addAll(aceptarButtonType, cancelarButtonType);

        // Crear contenido organizado con VBox
        VBox contentBox = new VBox(12);
        contentBox.setPadding(new Insets(15));

        // Título
        Label titleLabel = new Label("Ingrese la cantidad para:");
        titleLabel.getStyleClass().add("dialog-title");

        // Información del producto con colores diferenciados
        VBox productBox = new VBox(4);

        if (itemCode != null && !itemCode.isEmpty()) {
            javafx.scene.layout.HBox codeBox = new javafx.scene.layout.HBox(8);
            Label codeLabel = new Label("🏷️ Código:");
            codeLabel.setStyle("-fx-text-fill: #3ca0aa; -fx-font-size: 11px; -fx-font-weight: bold; -fx-min-width: 80px;");
            Label codeValue = new Label(itemCode);
            codeValue.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 11px;");
            codeBox.getChildren().addAll(codeLabel, codeValue);
            productBox.getChildren().add(codeBox);
        }

        if (itemDescription != null && !itemDescription.isEmpty()) {
            javafx.scene.layout.HBox descBox = new javafx.scene.layout.HBox(8);
            Label descLabel = new Label("📝 Descripción:");
            descLabel.setStyle("-fx-text-fill: #3ca0aa; -fx-font-size: 11px; -fx-font-weight: bold; -fx-min-width: 80px;");
            Label descValue = new Label(itemDescription);
            descValue.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 11px;");
            descValue.setWrapText(true);
            descValue.setMaxWidth(200);
            descBox.getChildren().addAll(descLabel, descValue);
            productBox.getChildren().add(descBox);
        }

        // TextField para la cantidad con funcionalidad mejorada
        TextField textField = new TextField("1.0");
        textField.setPromptText("Ej: 1.0, 2.5, 10 o con precio: 12 EU.L, 50.5   305.5");
        textField.setPrefWidth(200);
        textField.setMaxWidth(Double.MAX_VALUE);
        
        // Agregar información sobre el formato mejorado
        Label formatoLabel = new Label("💡 Formato mejorado: cantidad [espacios] precio_inicial");
        formatoLabel.setStyle("-fx-text-fill: #9ca3af; -fx-font-size: 10px; -fx-font-style: italic;");
        formatoLabel.setWrapText(true);
        formatoLabel.setMaxWidth(200);

        contentBox.getChildren().addAll(titleLabel, productBox, textField, formatoLabel);
        quantityDialog.getDialogPane().setContent(contentBox);

        // Aplicar estilos modernos
        applyDialogStyles(quantityDialog);
        setDialogIcon(quantityDialog, "fas-plus-circle");

        // Configurar el resultado del diálogo
        quantityDialog.setResultConverter(dialogButton -> {
            if (dialogButton == aceptarButtonType) {
                return textField.getText();
            }
            return null;
        });

        // Registrar el diálogo para manejo de bloqueo de pantalla
        activeDialogs.add(quantityDialog);
        
        // Configurar focus y selección
        Platform.runLater(() -> {
            textField.requestFocus();
            textField.selectAll();
        });

        // Mostrar el diálogo y limpiar registro al cerrar
        Optional<String> result = quantityDialog.showAndWait();
        activeDialogs.remove(quantityDialog);
        
        return result;
    }

    /**
     * Muestra un diálogo para seleccionar un Item de la lista encontrada.
     * Sigue el mismo estilo que otros diálogos y soporta navegación con teclado.
     *
     * MEJORAS IMPLEMENTADAS (JavaFX 21):
     * - Ancho aumentado a 800px para mostrar todas las columnas correctamente
     * - Ordenamiento automático por stock (mayor a menor, rojos al final)
     * - Columnas no reordenables para mantener consistencia
     * - Eliminación de celdas vacías para interfaz más limpia
     * - Estilos CSS modernos mejorados
     * - Mejor gestión de filas con stock cero/negativo
     *
     * @param items Lista de Items para seleccionar
     * @return Optional con el Item seleccionado, o vacío si se canceló
     */
    public Optional<Item> showItemSelectionDialog(List<Item> items) {
        if (items == null || items.isEmpty()) {
            return Optional.empty();
        }

        Dialog<Item> dialog = new Dialog<>();
        dialog.setTitle("Seleccionar Item");
        dialog.setHeaderText(null);

        // Configurar botones
        ButtonType seleccionarButtonType = new ButtonType("Seleccionar", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelarButtonType = new ButtonType("Cancelar", ButtonBar.ButtonData.CANCEL_CLOSE);
        dialog.getDialogPane().getButtonTypes().addAll(seleccionarButtonType, cancelarButtonType);

        // Crear contenido organizado con VBox
        VBox contentBox = new VBox(12);
        contentBox.setPadding(new Insets(15));

        // Título
        Label titleLabel = new Label("Se encontraron " + items.size() + " items. Seleccione uno:");
        titleLabel.getStyleClass().add("dialog-title");

        // Ordenar items por stock de mayor a menor (los rojos quedarán abajo)
        List<Item> sortedItems = items.stream()
                .sorted((item1, item2) -> {
                    Double stock1 = item1.getStockTotal() != null ? item1.getStockTotal() : 0.0;
                    Double stock2 = item2.getStockTotal() != null ? item2.getStockTotal() : 0.0;
                    return Double.compare(stock2, stock1); // Orden descendente
                })
                .toList();

        // TableView para mostrar los items de manera organizada
        TableView<Item> tableView = new TableView<>();
        tableView.getItems().addAll(sortedItems);

        // JavaFX 21: Calcular altura exacta basada en el número de items para eliminar celdas vacías
        int itemCount = sortedItems.size();
        double rowHeight = 25.0; // Altura estándar de fila
        double headerHeight = 30.0; // Altura del header
        double calculatedHeight = headerHeight + (itemCount * rowHeight) + 10; // +10 para padding
        double minHeight = Math.min(calculatedHeight, 400); // Máximo 400px

        tableView.setPrefHeight(minHeight);
        tableView.setMaxHeight(minHeight);
        tableView.setMinHeight(minHeight);

        // Aplicar estilos CSS similares a ProductoItemSearched
        tableView.getStyleClass().add("items-table-view");
        // JavaFX 21: Usar política de redimensionamiento fija para mantener anchos exactos
        tableView.setColumnResizePolicy(TableView.UNCONSTRAINED_RESIZE_POLICY);

        // Crear columnas similares a ProductoItemSearchedController (sin ubicación y campos dinámicos)
        // JavaFX 21: Deshabilitar reordenamiento de columnas para mantener orden consistente

        // Columna Marca - EXACTAMENTE como especificaste
        TableColumn<Item, String> marcaColumn = new TableColumn<>("Marca");
        marcaColumn.setPrefWidth(90.0); // Ancho exacto especificado
        marcaColumn.setMinWidth(90.0);
        marcaColumn.setMaxWidth(90.0);
        marcaColumn.setResizable(false); // No redimensionable para mantener ancho exacto
        marcaColumn.setReorderable(false); // JavaFX 21: Evitar intercambio de columnas
        marcaColumn.setCellValueFactory(cellData -> {
            Item item = cellData.getValue();
            String marcaText = item.getMarca() != null && item.getMarca().getNombre() != null ?
                    item.getMarca().getNombre().toUpperCase() : "SIN MARCA"; // MAYÚSCULAS como solicitaste
            return new SimpleStringProperty(marcaText);
        });

        // Columna Código Compuesto - EXACTAMENTE como especificaste
        TableColumn<Item, String> codCompuestoColumn = new TableColumn<>("Cod Compuesto");
        codCompuestoColumn.setPrefWidth(108.0); // Ancho exacto especificado
        codCompuestoColumn.setMinWidth(108.0);
        codCompuestoColumn.setMaxWidth(108.0);
        codCompuestoColumn.setResizable(false); // No redimensionable para mantener ancho exacto
        codCompuestoColumn.setReorderable(false); // JavaFX 21: Evitar intercambio de columnas
        codCompuestoColumn.setCellValueFactory(cellData -> {
            Item item = cellData.getValue();
            String codCompuesto = item.getCodCompuesto() != null ? item.getCodCompuesto().toUpperCase() : "";
            return new SimpleStringProperty(codCompuesto);
        });

        // Columna P.V 1 (Precio Venta Base) - EXACTAMENTE como especificaste
        TableColumn<Item, String> precioVentaBaseColumn = new TableColumn<>("P.V 1");
        precioVentaBaseColumn.setPrefWidth(54.0); // Ancho exacto especificado
        precioVentaBaseColumn.setMinWidth(54.0);
        precioVentaBaseColumn.setMaxWidth(54.0);
        precioVentaBaseColumn.setResizable(false); // No redimensionable para mantener ancho exacto
        precioVentaBaseColumn.setReorderable(false); // JavaFX 21: Evitar intercambio de columnas
        precioVentaBaseColumn.setCellValueFactory(cellData -> {
            Item item = cellData.getValue();
            Double price = item.getPrecioVentaBase();
            if (price == null) {
                return new SimpleStringProperty("");
            }
            return new SimpleStringProperty(EncryptionUtil.encrypt(price));
        });

        // Columna P.V 2 (Precio Venta Promoción) - EXACTAMENTE como especificaste
        TableColumn<Item, String> precioVentaPromocionColumn = new TableColumn<>("P.V 2");
        precioVentaPromocionColumn.setPrefWidth(54.0); // Ancho exacto especificado
        precioVentaPromocionColumn.setMinWidth(54.0);
        precioVentaPromocionColumn.setMaxWidth(54.0);
        precioVentaPromocionColumn.setResizable(false); // No redimensionable para mantener ancho exacto
        precioVentaPromocionColumn.setReorderable(false); // JavaFX 21: Evitar intercambio de columnas
        precioVentaPromocionColumn.setCellValueFactory(cellData -> {
            Item item = cellData.getValue();
            Double price = item.getPrecioVentaPromocion();
            if (price == null) {
                return new SimpleStringProperty("");
            }
            return new SimpleStringProperty(EncryptionUtil.encrypt(price));
        });

        // Columna P.V 3 (Precio Venta Público) - EXACTAMENTE como especificaste
        TableColumn<Item, String> precioVentaPublicoColumn = new TableColumn<>("P.V 3");
        precioVentaPublicoColumn.setPrefWidth(54.0); // Ancho exacto especificado
        precioVentaPublicoColumn.setMinWidth(54.0);
        precioVentaPublicoColumn.setMaxWidth(54.0);
        precioVentaPublicoColumn.setResizable(false); // No redimensionable para mantener ancho exacto
        precioVentaPublicoColumn.setReorderable(false); // JavaFX 21: Evitar intercambio de columnas
        precioVentaPublicoColumn.setCellValueFactory(cellData -> {
            Item item = cellData.getValue();
            Double price = item.getPrecioVentaPublico();
            if (price == null) {
                return new SimpleStringProperty("");
            }
            return new SimpleStringProperty(EncryptionUtil.encrypt(price));
        });

        // Columna Q (Stock Total) - EXACTAMENTE como especificaste
        TableColumn<Item, String> stockTotalColumn = new TableColumn<>("Q");
        stockTotalColumn.setPrefWidth(54.0); // Ancho exacto especificado
        stockTotalColumn.setMinWidth(54.0);
        stockTotalColumn.setMaxWidth(54.0);
        stockTotalColumn.setResizable(false); // No redimensionable para mantener ancho exacto
        stockTotalColumn.setReorderable(false); // JavaFX 21: Evitar intercambio de columnas
        stockTotalColumn.setCellValueFactory(cellData -> {
            Item item = cellData.getValue();
            if (item.getStockTotal() == null) {
                return new SimpleStringProperty("");
            }
            return new SimpleStringProperty(String.format("%.0f", item.getStockTotal()));
        });

        // Agregar todas las columnas al TableView
        tableView.getColumns().addAll(marcaColumn, codCompuestoColumn, precioVentaBaseColumn,
                                      precioVentaPromocionColumn, precioVentaPublicoColumn, stockTotalColumn);

        // JavaFX 21: Configurar row factory mejorado para eliminar completamente celdas vacías
        tableView.setRowFactory(tv -> {
            TableRow<Item> row = new TableRow<Item>() {
                @Override
                protected void updateItem(Item item, boolean empty) {
                    super.updateItem(item, empty);

                    if (item == null || empty) {
                        // JavaFX 21: Ocultar y desactivar completamente las filas vacías
                        setStyle("");
                        getStyleClass().removeAll("zero-stock-row");
                        setVisible(false);
                        setManaged(false);
                        setPrefHeight(0);
                        setMinHeight(0);
                        setMaxHeight(0);
                        return;
                    }

                    // JavaFX 21: Mostrar filas con contenido con altura normal
                    setVisible(true);
                    setManaged(true);
                    setPrefHeight(rowHeight);
                    setMinHeight(rowHeight);
                    setMaxHeight(rowHeight);

                    // Verificar si el stock es cero o negativo
                    if (item.getStockTotal() != null && item.getStockTotal() <= 0) {
                        // Aplicar clase CSS para filas con stock cero o negativo
                        if (!getStyleClass().contains("zero-stock-row")) {
                            getStyleClass().add("zero-stock-row");
                        }
                    } else {
                        // Remover la clase si el stock es positivo
                        getStyleClass().removeAll("zero-stock-row");
                    }
                }
            };
            return row;
        });

        // JavaFX 21: Configurar altura fija de celda para eliminar espacio extra
        tableView.setFixedCellSize(rowHeight);

        // Seleccionar el primer item por defecto
        if (!sortedItems.isEmpty()) {
            tableView.getSelectionModel().selectFirst();
            tableView.requestFocus();
        }

        // Información de navegación
        Label navigationLabel = new Label("💡 Use ↑↓ para navegar, Enter para seleccionar, Esc para cancelar");
        navigationLabel.getStyleClass().add("dialog-navigation-hint");
        navigationLabel.setWrapText(true);

        contentBox.getChildren().addAll(titleLabel, tableView, navigationLabel);
        dialog.getDialogPane().setContent(contentBox);

        // JavaFX 21: Calcular ancho exacto basado en los anchos especificados
        // Suma de anchos de columnas: 90 + 108 + 54 + 54 + 54 + 54 = 414px
        // Agregar espacio para bordes, padding, scrollbar y márgenes: ~250px
        // Total calculado: 664px, redondeado a 680px para asegurar visualización completa
        double calculatedWidth = 90 + 108 + 54 + 54 + 54 + 54 + 250; // 664px
        dialog.getDialogPane().setPrefWidth(680);
        dialog.getDialogPane().setMinWidth(680);
        dialog.getDialogPane().setMaxWidth(680); // Ancho fijo para mantener diseño consistente

        // Aplicar estilos modernos
        applyDialogStyles(dialog);
        setDialogIcon(dialog, "fas-table");

        // JavaFX 21: Configurar el diálogo para mejor centrado y comportamiento
        dialog.setResizable(true); // Permitir redimensionar si es necesario
        dialog.getDialogPane().setMinHeight(400); // Altura mínima para buena visualización

        // Habilitar/deshabilitar el botón de seleccionar basado en la selección
        javafx.scene.Node selectButton = dialog.getDialogPane().lookupButton(seleccionarButtonType);
        selectButton.setDisable(tableView.getSelectionModel().getSelectedItem() == null);

        tableView.getSelectionModel().selectedItemProperty().addListener((obs, oldSelection, newSelection) -> {
            selectButton.setDisable(newSelection == null);
        });

        // Configurar el resultado del diálogo
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == seleccionarButtonType) {
                return tableView.getSelectionModel().getSelectedItem();
            }
            return null;
        });

        // Agregar navegación con teclado
        tableView.setOnKeyPressed(event -> {
            switch (event.getCode()) {
                case ENTER -> {
                    if (tableView.getSelectionModel().getSelectedItem() != null) {
                        dialog.setResult(tableView.getSelectionModel().getSelectedItem());
                        dialog.close();
                    }
                }
                case ESCAPE -> {
                    dialog.setResult(null);
                    dialog.close();
                }
            }
        });

        // Permitir doble clic para seleccionar
        tableView.setOnMouseClicked(event -> {
            if (event.getClickCount() == 2 && tableView.getSelectionModel().getSelectedItem() != null) {
                dialog.setResult(tableView.getSelectionModel().getSelectedItem());
                dialog.close();
            }
        });

        // Registrar el diálogo para manejo de bloqueo de pantalla
        activeDialogs.add(dialog);
        
        // Configurar focus y limpieza
        Platform.runLater(() -> tableView.requestFocus());
        
        // Mostrar el diálogo y limpiar registro al cerrar
        Optional<Item> result = dialog.showAndWait();
        activeDialogs.remove(dialog);
        
        return result;
    }

    /**
     * Convierte un texto a Camel Case (primera letra de cada palabra en mayúscula)
     */
    private String toCamelCase(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        boolean nextUpperCase = true;

        for (char c : text.toLowerCase().toCharArray()) {
            if (Character.isSpaceChar(c) || c == '-' || c == '_') {
                nextUpperCase = true;
            } else if (nextUpperCase) {
                c = Character.toUpperCase(c);
                nextUpperCase = false;
            }

            if (!Character.isSpaceChar(c) && c != '-' && c != '_') {
                result.append(c);
            } else if (!result.isEmpty()) {
                result.append(' ');
            }
        }

        return result.toString();
    }

    /**
     * Cierra todos los diálogos activos. Útil para el manejo de bloqueo de pantalla.
     */
    public static void closeAllActiveDialogs() {
        log.debug("Cerrando {} diálogos activos debido a bloqueo de pantalla", activeDialogs.size());
        
        // Crear una copia de la lista para evitar ConcurrentModificationException
        List<Dialog<?>> dialogsToClose = new ArrayList<>(activeDialogs);
        
        for (Dialog<?> dialog : dialogsToClose) {
            try {
                Platform.runLater(() -> {
                    dialog.setResult(null);
                    dialog.close();
                });
            } catch (Exception e) {
                log.warn("Error al cerrar diálogo durante bloqueo de pantalla: {}", e.getMessage());
            }
        }
        
        activeDialogs.clear();
    }

    /**
     * Obtiene el texto de visualización para el tipo de venta.
     */
    private String getTipoVentaDisplayText(Sale.TipoVenta tipoVenta) {
        return switch (tipoVenta) {
            case PROFORMA -> "Venta al Contado";
            case CREDITO -> "Venta a Crédito";
            case PEDIDO -> "Pedido";
            case CONTADO -> "Venta al Contado";
        };
    }

    /**
     * Obtiene el texto del estado según el tipo de venta.
     */
    private String getEstadoVentaText(Sale.TipoVenta tipoVenta) {
        return switch (tipoVenta) {
            case PROFORMA -> "Pendiente de pago (30 min)";
            case CREDITO -> "Pendiente de pago a crédito";
            case PEDIDO -> "Pendiente de entrega";
            case CONTADO -> "Pendiente de pago";
        };
    }

    /**
     * Aplica estilos modernos a un diálogo.
     */
    private void applyDialogStyles(Dialog<?> dialog) {
        // Hacer el diálogo sin decoraciones para un diseño moderno
        dialog.initStyle(StageStyle.UNDECORATED);

        // Aplicar estilos CSS
        dialog.getDialogPane().getStylesheets().addAll(
                getClass().getResource(STYLESHEET_PATH).toExternalForm(),
                getClass().getResource(SEARCH_PRODUCT_STYLESHEET_PATH).toExternalForm()
        );

        // Hacer el diálogo arrastrable
        makeDraggable(dialog);
    }

    /**
     * Configura un icono para el diálogo.
     */
    private void setDialogIcon(Dialog<?> dialog, String iconClass) {
        // Implementación básica - se puede expandir con iconos reales
        log.debug("Configurando icono {} para diálogo", iconClass);
    }

    /**
     * Hace que el diálogo sea arrastrable.
     */
    private void makeDraggable(Dialog<?> dialog) {
        final double[] xOffset = {0};
        final double[] yOffset = {0};

        dialog.getDialogPane().setOnMousePressed(event -> {
            xOffset[0] = event.getSceneX();
            yOffset[0] = event.getSceneY();
        });

        dialog.getDialogPane().setOnMouseDragged(event -> {
            dialog.setX(event.getScreenX() - xOffset[0]);
            dialog.setY(event.getScreenY() - yOffset[0]);
        });
    }

    /**
     * Muestra un diálogo para editar un campo de BienServicioCargado.
     * 
     * @param title Título del diálogo
     * @param fieldName Nombre del campo a editar
     * @param currentValue Valor actual del campo
     * @param isDecimal Indica si el campo es decimal
     * @return El nuevo valor ingresado, o null si se canceló
     */
    public Optional<String> showBienServicioEditDialog(String title, String fieldName, String currentValue, boolean isDecimal) {
        Dialog<String> dialog = new Dialog<>();
        dialog.setTitle(title);
        dialog.setHeaderText(null);

        // Configurar botones
        ButtonType aceptarButtonType = new ButtonType("Aceptar", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelarButtonType = new ButtonType("Cancelar", ButtonBar.ButtonData.CANCEL_CLOSE);
        dialog.getDialogPane().getButtonTypes().addAll(aceptarButtonType, cancelarButtonType);

        // Crear contenido organizado con VBox
        VBox contentBox = new VBox(12);
        contentBox.setPadding(new Insets(15));

        // Título
        Label titleLabel = new Label("Editar " + fieldName + ":");
        titleLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 13px; -fx-font-weight: 600;");

        // TextField para el valor
        TextField textField = new TextField(currentValue);
        textField.setPromptText("Ingrese el nuevo valor");
        textField.setPrefWidth(250);
        textField.setMaxWidth(Double.MAX_VALUE);

        // Si es un campo decimal, configurar validación
        if (isDecimal) {
            Pattern decimalPattern = Pattern.compile("^\\d*\\.?\\d{0,2}$");
            textField.textProperty().addListener((obs, oldVal, newVal) -> {
                if (newVal != null && !newVal.isEmpty() && !decimalPattern.matcher(newVal).matches()) {
                    textField.setText(oldVal); // Revertir a valor anterior si no es válido
                }
            });
        }

        contentBox.getChildren().addAll(titleLabel, textField);
        dialog.getDialogPane().setContent(contentBox);

        // Aplicar estilos modernos
        applyDialogStyles(dialog);
        setDialogIcon(dialog, "fas-edit");

        // Configurar el resultado del diálogo
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == aceptarButtonType) {
                String value = textField.getText().trim();
                if (value.isEmpty()) {
                    return null;
                }

                // Validar formato si es decimal
                if (isDecimal) {
                    try {
                        Double.parseDouble(value);
                        return value;
                    } catch (NumberFormatException e) {
                        return null;
                    }
                }

                return value;
            }
            return null;
        });

        // Configurar focus y selección
        Platform.runLater(() -> {
            textField.requestFocus();
            textField.selectAll();
        });

        return dialog.showAndWait();
    }
}

package corp.jamaro.jamaroescritoriofx.appfx.ventas.model;

import corp.jamaro.jamaroescritoriofx.appfx.caja.model.CobroDineroProgramado;
import corp.jamaro.jamaroescritoriofx.appfx.caja.model.DevolucionDinero;
import corp.jamaro.jamaroescritoriofx.appfx.model.Cliente;
import corp.jamaro.jamaroescritoriofx.appfx.model.User;
import lombok.Data;

import java.time.Instant;
import java.util.List;
import java.util.Set;
import java.util.UUID;

@Data
public class Sale {
    private UUID id;

    private User iniciadaPor;//user que inició la venta

    private Cliente cliente;

    private List<BienServicioCargado> bienServicioCargados;
    private List<BienServicioDevuelto> bienServicioDevueltos;

    private Set<CobroDineroProgramado> dineroCobros;
    private Set<DevolucionDinero> dineroDevoluciones;

    private Double totalMontoInicial; // el monto total antes de los descuentos en soles
    private Double totalMontoAcordado; //el monto total acordado a pagar en Soles

    private Double totalRestante;// el monto total restante a pagar sirve para credito y para pedido cuando fue al contado es cero

    private Boolean estaPagadoEntregado=false; //para saber si terminó de ser pagado o entregado

    private TipoVenta tipoVenta = TipoVenta.PROFORMA;

    private Instant createdAt=Instant.now();


    //la moneda que usa por defecto el sistema es Soles no olvidar
    public enum TipoVenta {
        PROFORMA,
        CONTADO,
        CREDITO,
        PEDIDO
    }
}

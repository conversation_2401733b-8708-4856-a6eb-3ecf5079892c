package corp.jamaro.jamaroescritoriofx.appfx.caja.model;

import corp.jamaro.jamaroescritoriofx.appfx.dinero.model.Dinero;
import corp.jamaro.jamaroescritoriofx.appfx.model.User;
import lombok.Data;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
public class PagoDineroProgramado {
    private UUID id;

    private String motivo;
    private Double monto;
    private User programadoPor;

    private String detalles;

    private Instant creadoEl = Instant.now();
    private Instant fechaLimite;

    private Set<Dinero> dineroPagado;// aquí se puede ver quien recibe el dinero y más detalles que tiene el model dinero

    private Boolean estaPagado = false;
}

package corp.jamaro.jamaroescritoriofx.appfx.caja.service;

import corp.jamaro.jamaroescritoriofx.connection.service.ConnectionService;
import corp.jamaro.jamaroescritoriofx.appfx.caja.model.gui.CajaGui;
import corp.jamaro.jamaroescritoriofx.appfx.caja.model.CobroDineroProgramado;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class CajaGuiService {

    private static final String ROUTE_SUBSCRIBE = "caja.gui.subscribe";
    private static final String ROUTE_SUBSCRIBE_CONTADO = "caja.gui.subscribeCobroDineroProgramadoContado";
    private static final String ROUTE_SUBSCRIBE_CREDITO = "caja.gui.subscribeCobroDineroProgramadoCredito";
    private static final String ROUTE_GET_ALL = "caja.gui.getAll";
    private static final String ROUTE_CREATE = "caja.gui.create";
    private static final String ROUTE_UPDATE_CONFIG = "caja.gui.updateConfig";
    private static final String ROUTE_INITIALIZE_EFECTIVO = "caja.gui.initializeCajaDineroEfectivo";
    private static final String ROUTE_CLOSE_EFECTIVO = "caja.gui.closeCajaDineroEfectivo";
    private static final String ROUTE_INITIALIZE_DIGITAL = "caja.gui.initializeCajaDineroDigital";
    private static final String ROUTE_CLOSE_DIGITAL = "caja.gui.closeCajaDineroDigital";

    private final ConnectionService connectionService;

    /**
     * Subscribe to CajaGui changes for a specific CajaGui ID
     * @param cajaGuiId The id of the CajaGui (UUID)
     * @return A Flux<CajaGui> with real-time updates.
     */
    public Flux<CajaGui> subscribeToCajaGuiChanges(UUID cajaGuiId) {
        log.debug("Subscribing to CajaGui changes for id: {}", cajaGuiId);
        var request = new SubscribeToCajaGuiRequest(cajaGuiId);
        return connectionService.authenticatedSubscription(ROUTE_SUBSCRIBE, request, CajaGui.class);
    }

    /**
     * Subscribe to CobroDineroProgramado changes for CONTADO sales
     * @return A Flux<CobroDineroProgramado> for CONTADO sales
     */
    public Flux<CobroDineroProgramado> subscribeToCobroDineroProgramadoContadoChanges() {
        log.debug("Subscribing to CobroDineroProgramado changes for CONTADO");
        return connectionService.authenticatedSubscription(ROUTE_SUBSCRIBE_CONTADO, "", CobroDineroProgramado.class);
    }

    /**
     * Subscribe to CobroDineroProgramado changes for CREDITO sales
     * @return A Flux<CobroDineroProgramado> for CREDITO sales
     */
    public Flux<CobroDineroProgramado> subscribeToCobroDineroProgramadoCreditoChanges() {
        log.debug("Subscribing to CobroDineroProgramado changes for CREDITO");
        return connectionService.authenticatedSubscription(ROUTE_SUBSCRIBE_CREDITO, "", CobroDineroProgramado.class);
    }

    /**
     * Get all CajaGui ordered by createdAt
     * @return Flux of CajaGui ordered by createdAt
     */
    public Flux<CajaGui> getAllCajaGuiOrderedByCreatedAt() {
        log.debug("Getting all CajaGui ordered by createdAt");
        return connectionService.authenticatedSubscription(ROUTE_GET_ALL, "", CajaGui.class);
    }

    /**
     * Create a new CajaGui
     * @param nombreCaja Name of the CajaGui
     * @param guiConfig Optional GUI configuration in JSON format
     * @return Mono of the created CajaGui
     */
    public Mono<CajaGui> createCajaGui(String nombreCaja, String guiConfig) {
        log.debug("Creating new CajaGui with name: {}", nombreCaja);
        var request = new CreateCajaGuiRequest(nombreCaja, guiConfig);
        return connectionService.authenticatedRequest(ROUTE_CREATE, request, CajaGui.class);
    }

    /**
     * Update the guiConfig of a CajaGui
     * @param id ID of the CajaGui
     * @param guiConfig GUI configuration in JSON format
     * @return Mono of the updated CajaGui
     */
    public Mono<CajaGui> updateCajaGuiConfig(UUID id, String guiConfig) {
        log.debug("Updating CajaGui config for id: {}", id);
        var request = new UpdateCajaGuiConfigRequest(id, guiConfig);
        return connectionService.authenticatedRequest(ROUTE_UPDATE_CONFIG, request, CajaGui.class);
    }

    /**
     * Initialize a CajaDineroEfectivo and assign it to a CajaGui
     * @param cajaGuiId ID of the CajaGui
     * @param nombre Name of the CajaDineroEfectivo
     * @param montoInicial Optional initial amount
     * @param inicioDiezCentimos Optional number of 10 cent coins
     * @param inicioVeinteCentimos Optional number of 20 cent coins
     * @param inicioCincuentaCentimos Optional number of 50 cent coins
     * @param inicioUnSol Optional number of 1 sol coins
     * @param inicioDosSoles Optional number of 2 sol coins
     * @param inicioCincoSoles Optional number of 5 sol coins
     * @param inicioDiezSoles Optional number of 10 sol bills
     * @param inicioVeinteSoles Optional number of 20 sol bills
     * @param inicioCincuentaSoles Optional number of 50 sol bills
     * @param inicioCienSoles Optional number of 100 sol bills
     * @param inicioDoscientosSoles Optional number of 200 sol bills
     * @return Mono of the updated CajaGui
     */
    public Mono<CajaGui> initializeCajaDineroEfectivo(
            UUID cajaGuiId,
            String nombre,
            Double montoInicial,
            Integer inicioDiezCentimos,
            Integer inicioVeinteCentimos,
            Integer inicioCincuentaCentimos,
            Integer inicioUnSol,
            Integer inicioDosSoles,
            Integer inicioCincoSoles,
            Integer inicioDiezSoles,
            Integer inicioVeinteSoles,
            Integer inicioCincuentaSoles,
            Integer inicioCienSoles,
            Integer inicioDoscientosSoles) {

        log.debug("Initializing CajaDineroEfectivo for CajaGui id: {}", cajaGuiId);
        var request = new InitializeCajaDineroEfectivoRequest(
                cajaGuiId, nombre, montoInicial,
                inicioDiezCentimos, inicioVeinteCentimos, inicioCincuentaCentimos,
                inicioUnSol, inicioDosSoles, inicioCincoSoles,
                inicioDiezSoles, inicioVeinteSoles, inicioCincuentaSoles,
                inicioCienSoles, inicioDoscientosSoles
        );
        return connectionService.authenticatedRequest(ROUTE_INITIALIZE_EFECTIVO, request, CajaGui.class);
    }

    /**
     * Close a CajaDineroEfectivo
     * @param cajaGuiId ID of the CajaGui
     * @param cajaDineroEfectivoId ID of the CajaDineroEfectivo
     * @param cierreDiezCentimos Number of 10 cent coins at closing
     * @param cierreVeinteCentimos Number of 20 cent coins at closing
     * @param cierreCincuentaCentimos Number of 50 cent coins at closing
     * @param cierreUnSol Number of 1 sol coins at closing
     * @param cierreDosSoles Number of 2 sol coins at closing
     * @param cierreCincoSoles Number of 5 sol coins at closing
     * @param cierreDiezSoles Number of 10 sol bills at closing
     * @param cierreVeinteSoles Number of 20 sol bills at closing
     * @param cierreCincuentaSoles Number of 50 sol bills at closing
     * @param cierreCienSoles Number of 100 sol bills at closing
     * @param cierreDoscientosSoles Number of 200 sol bills at closing
     * @return Mono of the updated CajaGui
     */
    public Mono<CajaGui> closeCajaDineroEfectivo(
            UUID cajaGuiId,
            UUID cajaDineroEfectivoId,
            Integer cierreDiezCentimos,
            Integer cierreVeinteCentimos,
            Integer cierreCincuentaCentimos,
            Integer cierreUnSol,
            Integer cierreDosSoles,
            Integer cierreCincoSoles,
            Integer cierreDiezSoles,
            Integer cierreVeinteSoles,
            Integer cierreCincuentaSoles,
            Integer cierreCienSoles,
            Integer cierreDoscientosSoles) {

        log.debug("Closing CajaDineroEfectivo id: {} for CajaGui id: {}", cajaDineroEfectivoId, cajaGuiId);
        var request = new CloseCajaDineroEfectivoRequest(
                cajaGuiId, cajaDineroEfectivoId,
                cierreDiezCentimos, cierreVeinteCentimos, cierreCincuentaCentimos,
                cierreUnSol, cierreDosSoles, cierreCincoSoles,
                cierreDiezSoles, cierreVeinteSoles, cierreCincuentaSoles,
                cierreCienSoles, cierreDoscientosSoles
        );
        return connectionService.authenticatedRequest(ROUTE_CLOSE_EFECTIVO, request, CajaGui.class);
    }

    /**
     * Initialize a CajaDineroDigital and assign it to a CajaGui
     * @param cajaGuiId ID of the CajaGui
     * @param cuentaDigitalAsignada Digital account assigned details
     * @param montoInicialDigital Initial digital amount
     * @return Mono of the updated CajaGui
     */
    public Mono<CajaGui> initializeCajaDineroDigital(
            UUID cajaGuiId,
            String cuentaDigitalAsignada,
            Double montoInicialDigital) {

        log.debug("Initializing CajaDineroDigital for CajaGui id: {}", cajaGuiId);
        var request = new InitializeCajaDineroDigitalRequest(cajaGuiId, cuentaDigitalAsignada, montoInicialDigital);
        return connectionService.authenticatedRequest(ROUTE_INITIALIZE_DIGITAL, request, CajaGui.class);
    }

    /**
     * Close a CajaDineroDigital
     * @param cajaGuiId ID of the CajaGui
     * @param cajaDineroDigitalId ID of the CajaDineroDigital
     * @param cierreDigitalDeclarado Declared digital closing amount
     * @return Mono of the updated CajaGui
     */
    public Mono<CajaGui> closeCajaDineroDigital(
            UUID cajaGuiId,
            UUID cajaDineroDigitalId,
            Double cierreDigitalDeclarado) {

        log.debug("Closing CajaDineroDigital id: {} for CajaGui id: {}", cajaDineroDigitalId, cajaGuiId);
        var request = new CloseCajaDineroDigitalRequest(cajaGuiId, cajaDineroDigitalId, cierreDigitalDeclarado);
        return connectionService.authenticatedRequest(ROUTE_CLOSE_DIGITAL, request, CajaGui.class);
    }

    // Records for request parameters

    public record SubscribeToCajaGuiRequest(UUID cajaGuiId) {}

    public record CreateCajaGuiRequest(String nombreCaja, String guiConfig) {}

    public record UpdateCajaGuiConfigRequest(UUID id, String guiConfig) {}

    public record InitializeCajaDineroEfectivoRequest(
            UUID cajaGuiId,
            String nombre,
            Double montoInicial,
            Integer inicioDiezCentimos,
            Integer inicioVeinteCentimos,
            Integer inicioCincuentaCentimos,
            Integer inicioUnSol,
            Integer inicioDosSoles,
            Integer inicioCincoSoles,
            Integer inicioDiezSoles,
            Integer inicioVeinteSoles,
            Integer inicioCincuentaSoles,
            Integer inicioCienSoles,
            Integer inicioDoscientosSoles
    ) {}

    public record CloseCajaDineroEfectivoRequest(
            UUID cajaGuiId,
            UUID cajaDineroEfectivoId,
            Integer cierreDiezCentimos,
            Integer cierreVeinteCentimos,
            Integer cierreCincuentaCentimos,
            Integer cierreUnSol,
            Integer cierreDosSoles,
            Integer cierreCincoSoles,
            Integer cierreDiezSoles,
            Integer cierreVeinteSoles,
            Integer cierreCincuentaSoles,
            Integer cierreCienSoles,
            Integer cierreDoscientosSoles
    ) {}

    public record InitializeCajaDineroDigitalRequest(
            UUID cajaGuiId,
            String cuentaDigitalAsignada,
            Double montoInicialDigital
    ) {}

    public record CloseCajaDineroDigitalRequest(
            UUID cajaGuiId,
            UUID cajaDineroDigitalId,
            Double cierreDigitalDeclarado
    ) {}
}

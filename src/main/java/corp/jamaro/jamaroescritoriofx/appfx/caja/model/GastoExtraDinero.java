package corp.jamaro.jamaroescritoriofx.appfx.caja.model;

import corp.jamaro.jamaroescritoriofx.appfx.dinero.model.Dinero;
import corp.jamaro.jamaroescritoriofx.appfx.model.User;
import lombok.Data;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
public class GastoExtraDinero {
    private UUID id;

    private String motivo;
    private Double monto;
    private User programadoPor;//user que programo el gasto en el campo dineroGastado ya podemos ver quien entrego el dinero

    private TipoDeGasto tipoDeGasto;
    private String detalles;

    private Instant realizadoEl = Instant.now();

    private Set<Dinero> dineroGastado;//dinero del tipo esEntrada false

    public enum TipoDeGasto {
        PROVEEDOR,
        FLETE,
        TIENDA,
        TRAMITES,
        MERCADO,
        PAPA,
        MAMA,
        OTROS
    }
}

package corp.jamaro.jamaroescritoriofx.appfx.caja.service;

import corp.jamaro.jamaroescritoriofx.connection.service.ConnectionService;
import corp.jamaro.jamaroescritoriofx.appfx.caja.model.CobroDineroProgramado;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

/**
 * Servicio cliente para gestionar las operaciones relacionadas con cobros de caja.
 * Se comunica con el servidor a través de RSocket para procesar cobros y obtener
 * información sobre cobros pendientes.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CajaCobrosService {

    private static final String ROUTE_PROCESAR_COBRO = "cajaCobros.procesarCobro";
    private static final String ROUTE_COBROS_PENDIENTES_CONTADO = "cajaCobros.obtenerCobrosPendientesContado";
    private static final String ROUTE_COBROS_PENDIENTES_CREDITO = "cajaCobros.obtenerCobrosPendientesCredito";
    private static final String ROUTE_COBROS_PENDIENTES_PEDIDO = "cajaCobros.obtenerCobrosPendientesPedido";

    private final ConnectionService connectionService;

    /**
     * Procesa un cobro para un CobroDineroProgramado específico.
     * Permite pagos en efectivo, digital o mixtos.
     * 
     * @param cajaGuiId ID de la caja donde se realiza el cobro
     * @param cobroDineroProgramadoId ID del cobro programado a procesar
     * @param montoEfectivo Monto pagado en efectivo (puede ser null o 0)
     * @param montoDigital Monto pagado digitalmente (puede ser null o 0)
     * @param detallesEfectivo Detalles del pago en efectivo (opcional)
     * @param detallesDigital Detalles del pago digital (opcional)
     * @return Mono con el resultado del cobro procesado
     */
    public Mono<ResultadoCobro> procesarCobro(
            UUID cajaGuiId,
            UUID cobroDineroProgramadoId,
            Double montoEfectivo,
            Double montoDigital,
            String detallesEfectivo,
            String detallesDigital) {

        log.debug("Procesando cobro para CobroDineroProgramado ID: {}, CajaGui ID: {}", 
                cobroDineroProgramadoId, cajaGuiId);

        var request = new ProcesarCobroRequest(
                cajaGuiId,
                cobroDineroProgramadoId,
                montoEfectivo,
                montoDigital,
                detallesEfectivo,
                detallesDigital
        );

        return connectionService.authenticatedRequest(ROUTE_PROCESAR_COBRO, request, ResultadoCobro.class);
    }

    /**
     * Obtiene todos los cobros pendientes para ventas al contado.
     * Estos son cobros que aún no han sido completados y pertenecen a ventas de tipo CONTADO.
     * 
     * @return Flux de cobros pendientes para ventas al contado
     */
    public Flux<CobroDineroProgramado> obtenerCobrosPendientesContado() {
        log.debug("Obteniendo cobros pendientes para ventas al contado");
        return connectionService.authenticatedSubscription(ROUTE_COBROS_PENDIENTES_CONTADO, "", CobroDineroProgramado.class);
    }

    /**
     * Obtiene todos los cobros pendientes para ventas a crédito.
     * Estos son cobros que aún no han sido completados y pertenecen a ventas de tipo CREDITO.
     * 
     * @return Flux de cobros pendientes para ventas a crédito
     */
    public Flux<CobroDineroProgramado> obtenerCobrosPendientesCredito() {
        log.debug("Obteniendo cobros pendientes para ventas a crédito");
        return connectionService.authenticatedSubscription(ROUTE_COBROS_PENDIENTES_CREDITO, "", CobroDineroProgramado.class);
    }

    /**
     * Obtiene todos los cobros pendientes para ventas de pedido.
     * Estos son cobros que aún no han sido completados y pertenecen a ventas de tipo PEDIDO.
     * 
     * @return Flux de cobros pendientes para ventas de pedido
     */
    public Flux<CobroDineroProgramado> obtenerCobrosPendientesPedido() {
        log.debug("Obteniendo cobros pendientes para ventas de pedido");
        return connectionService.authenticatedSubscription(ROUTE_COBROS_PENDIENTES_PEDIDO, "", CobroDineroProgramado.class);
    }

    /**
     * DTO para representar el resultado de un cobro procesado.
     * Contiene información sobre el éxito de la operación, mensajes informativos,
     * el vuelto a entregar y el cobro procesado.
     */
    public static class ResultadoCobro {
        private final boolean exitoso;
        private final String mensaje;
        private final Double vuelto;
        private final CobroDineroProgramado cobroProcesado;

        public ResultadoCobro() {
            this.exitoso = false;
            this.mensaje = "";
            this.vuelto = 0.0;
            this.cobroProcesado = null;
        }

        public ResultadoCobro(boolean exitoso, String mensaje, Double vuelto, CobroDineroProgramado cobroProcesado) {
            this.exitoso = exitoso;
            this.mensaje = mensaje;
            this.vuelto = vuelto;
            this.cobroProcesado = cobroProcesado;
        }

        /**
         * @return true si el cobro fue procesado exitosamente, false en caso contrario
         */
        public boolean isExitoso() { 
            return exitoso; 
        }

        /**
         * @return Mensaje descriptivo del resultado del cobro
         */
        public String getMensaje() { 
            return mensaje; 
        }

        /**
         * @return Monto del vuelto a entregar al cliente (0.0 si no hay vuelto)
         */
        public Double getVuelto() { 
            return vuelto; 
        }

        /**
         * @return El cobro programado que fue procesado con su estado actualizado
         */
        public CobroDineroProgramado getCobroProcesado() { 
            return cobroProcesado; 
        }
    }

    /**
     * Record para encapsular los datos necesarios para procesar un cobro.
     * Contiene toda la información requerida para realizar un pago en efectivo,
     * digital o mixto.
     */
    public record ProcesarCobroRequest(
            UUID cajaGuiId,
            UUID cobroDineroProgramadoId,
            Double montoEfectivo,
            Double montoDigital,
            String detallesEfectivo,
            String detallesDigital
    ) {}
}

package corp.jamaro.jamaroescritoriofx.appfx.caja.model;

import corp.jamaro.jamaroescritoriofx.appfx.dinero.model.Dinero;
import corp.jamaro.jamaroescritoriofx.appfx.model.User;
import lombok.Data;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
public class DevolucionDinero {
    private UUID id;

    private Double montoADevolver;//el monto en Soles ya que el sistema trabajar por defecto en soles a devolver

    private Set<Dinero> dineroDevuelto;//aqui podemos ver que user o users devolvieron el dinero ya que dinero tiene ese campo

    private User iniciadoPor;//user que programo la devolucion
    private Instant creadoEl = Instant.now();
    private Instant devueltoEl;

    private Boolean estaDevuelto = false;// true (devuelto) false (no devuelto) para encontrar facilmente que dinero faltan devolver en caja.
}

package corp.jamaro.jamaroescritoriofx.appfx.caja.model;

import corp.jamaro.jamaroescritoriofx.appfx.dinero.model.Dinero;
import corp.jamaro.jamaroescritoriofx.appfx.model.User;
import lombok.Data;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
public class CajaDineroEfectivo {
    private UUID id;

    private String nombre; // Por si luego ponemos varias cajas

    //inicio
    private User abiertaPor;
    private Instant abiertaEl = Instant.now();
    private Double montoInicialEfectivo; // en soles

    //detalles de inicio de caja opcionales
    private Integer inicioDiezCentimos;//monedas de 10 centimos
    private Integer inicioVeinteCentimos;//monedas de 20 centimos
    private Integer inicioCincuentaCentimos;//monedas de 50 centimos
    private Integer inicioUnSol;//monedas de 1 sol
    private Integer inicioDosSoles;//monedas de 2 soles
    private Integer inicioCincoSoles;//monedas de 5 soles
    private Integer inicioDiezSoles;//billetes de 10 soles
    private Integer inicioVeinteSoles;//billetes de 20 soles
    private Integer inicioCincuentaSoles;//billetes de 50 soles
    private Integer inicioCienSoles;//billetes de 100 soles
    private Integer inicioDoscientosSoles;//billetes de 200 soles

    //Cierre de caja con dinero en efectivo
    private User cerradaPor;// User que cierra la caja de dinero en efectivo.
    private Instant cerradaEl;

    private Double cierreEfectivoCalculado; // totalEntradasEfectivo - totalSalidasEfectivo + montoInicialEfectivo (dinero del tipo Efectivo)
    private Double cierreEfectivoDeclarado; // el cierre declarado por el usuario para comparar si cuadra la caja es El dinero efectivo presente en la caja.
    private Double diferenciaCierreEfectivo; // CierreEfectivo - cierreEfectivoDeclarado

    //detalles de cierre de caja
    private Integer cierreDiezCentimos;//monedas de 10 centimos
    private Integer cierreVeinteCentimos;//monedas de 20 centimos
    private Integer cierreCincuentaCentimos;//monedas de 50 centimos
    private Integer cierreUnSol;//monedas de 1 sol
    private Integer cierreDosSoles;//monedas de 2 soles
    private Integer cierreCincoSoles;//monedas de 5 soles
    private Integer cierreDiezSoles;//billetes de 10 soles
    private Integer cierreVeinteSoles;//billetes de 20 soles
    private Integer cierreCincuentaSoles;//billetes de 50 soles
    private Integer cierreCienSoles;//billetes de 100 soles
    private Integer cierreDoscientosSoles;//billetes de 200 soles


    //Datos de entrega y conteo de dinero en efectivo para llevar al banco
    private User cierreEfectivoContadoPor;    
    private Instant contadoEl;//fecha que entrega y cuenta el dinero para llevar al banco

    private Double cierreEfectivoEntregado; // el total entregado para ser contado antes de llevar al banco.
    private Double diferenciaCierreEntregado;// cierreEfectivoDeclarado - CierreEfectivoEntregado
    private String detallesCierreEfectivoEntregado;//opcional cualquier detalle extra o aclaración en caso, falte dinero o sobre.
    private Boolean entregado = false;// campo para controlar que la caja fue cerrada, entregada y contada.

    //DETALLES DEL CONTEO PARA LLEVAR AL BANCO SOLO LLEVAMOS BILLETES
    private Integer conteoDiezSoles;
    private Integer conteoVeinteSoles;
    private Integer conteoCincuentaSoles;
    private Integer conteoCienSoles;
    private Integer conteoDoscientosSoles;

    //CAMPOS PARA CALCULO
    private Double totalEntradasEfectivo;//Suma de Dinero.montoReal de todos los Dinero con esEntrada = true y tipoDeDinero = EFECTIVO
    private Double totalSalidasEfectivo;//Suma de Dinero.montoReal de todos los Dinero con esEntrada = false y tipoDeDinero = EFECTIVO

    //Datos extra de interés que no se usan pal cálculo de cierre, pero si para estadísticas.
    private Double pagosVentaContadoEfectivo;
    private Double pagosVentaCreditoEfectivo;
    private Double pagosVentaPedidoEfectivo;
    private Double devolucionesVentaEfectivo;
    private Double ingresosExtraEfectivo;
    private Double gastosExtraEfectivo;
    private Double pagosDineroProgramadoEfectivo;


    // Registros de dinero en efectivo relacionados a esta caja se usan para cálculos.
    private Set<Dinero> entradasEfectivo; // Entradas de dinero en efectivo asociadas a la caja
    private Set<Dinero> salidasEfectivo; // Salidas de dinero en efectivo asociadas a la caja

    private Boolean estaCerrada = false;
}

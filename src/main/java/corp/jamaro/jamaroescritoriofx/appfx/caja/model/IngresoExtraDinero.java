package corp.jamaro.jamaroescritoriofx.appfx.caja.model;

import corp.jamaro.jamaroescritoriofx.appfx.dinero.model.Dinero;
import corp.jamaro.jamaroescritoriofx.appfx.model.User;
import lombok.Data;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
public class IngresoExtraDinero {
    private UUID id;

    private String motivo; // Motivo del ingreso
    private Double monto; // Monto del ingreso en soles
    private User realizadoPor; // User que realizó el ingreso

    private Instant realizadoEl = Instant.now();

    private Set<Dinero> dineroIngresado; // Dinero del tipo esEntrada true
}

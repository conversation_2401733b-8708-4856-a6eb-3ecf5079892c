package corp.jamaro.jamaroescritoriofx.appfx.caja.model;

import corp.jamaro.jamaroescritoriofx.appfx.dinero.model.Dinero;
import corp.jamaro.jamaroescritoriofx.appfx.model.User;
import lombok.Data;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
public class CobroDineroProgramado {

    private UUID id;

    private Double montoACobrar;//el monto en Soles ya que el sistema trabajar por defecto en soles.
    private Double montoRestante;//el monto restante a cobrar, se actualiza cada vez que se cobra dinero.

    private Set<Dinero> dineroCobrados;//aquí podemos ver quien o quienes cobraron el dinero ya que dinero tiene campo user

    private User iniciadoPor;//user que programo el cobro

    private Instant creadoEl = Instant.now();
    private Instant fechaLimite;

    private Instant terminadoDeCobrarEl;

    private Boolean estaCobrado = false;// true (cobrado por completo) false (no cobrado por completo) para encontrar facilmente que dinero faltan cobrar en caja.
}

package corp.jamaro.jamaroescritoriofx.appfx.caja.model;

import corp.jamaro.jamaroescritoriofx.appfx.dinero.model.Dinero;
import corp.jamaro.jamaroescritoriofx.appfx.model.User;
import lombok.Data;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
public class CajaDineroDigital {
    private UUID id;

    private String cuentaDigitalAsignada;//detalles de la cuenta digital asignada, número de cuenta, etc.

    //inicio
    private User abiertaPor;
    private Instant abiertaEl = Instant.now();
    private Double montoInicialDigital; // en soles

    //Cierre digital
    private User cerradaPor;
    private Instant cerradaEl;

    private Double cierreDigitalCalculado; // totalEntradasDigital - totalSalidasDigital + montoInicialDigital
    private Double cierreDigitalDeclarado;
    private Double diferenciaCierreDigital;

    //CAMPOS PARA CALCULO
    private Double totalEntradasDigital;//Suma de Dinero.montoReal de todos los Dinero con esEntrada = true y tipoDeDinero = DIGITAL
    private Double totalSalidasDigital;//Suma de Dinero.montoReal de todos los Dinero con esEntrada = false y tipoDeDinero = DIGITAL

    //Datos extra de interés que no se usan pal cálculo de cierre, pero si para estadísticas.
    private Double pagosVentaContadoDigital;
    private Double pagosVentaCreditoDigital;
    private Double pagosVentaPedidoDigital;
    private Double devolucionesVentaDigital;
    private Double ingresosExtraDigital;
    private Double gastosExtraDigital;
    private Double pagosDineroProgramadoDigital;
    
    private Set<Dinero> entradasDigital;
    private Set<Dinero> salidasDigital;

    private Boolean estaCerrada = false;
}